/* Barcode-to-PC Client Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.connection-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 1rem;
    opacity: 0.9;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ff4444;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #44ff44;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Scanner Section */
.scanner-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.scanner-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    background: #000;
}

.scanner-video {
    width: 100%;
    height: 300px;
    object-fit: cover;
    display: block;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.scan-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid #00ff00;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,255,0,0.5);
}

.scan-line {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 180px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scanLine 2s linear infinite;
}

@keyframes scanLine {
    0% { transform: translate(-50%, -50%) translateY(-90px); }
    100% { transform: translate(-50%, -50%) translateY(90px); }
}

.scanner-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-link {
    background: none;
    color: white;
    text-decoration: underline;
    padding: 8px 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Cards */
.status-card, .scans-card, .manual-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.status-card h3, .scans-card h3, .manual-card h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.2rem;
}

/* Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.status-item:last-child {
    border-bottom: none;
}

.status-item .label {
    font-weight: 600;
    color: #6c757d;
}

/* Scans List */
.scans-list {
    max-height: 200px;
    overflow-y: auto;
}

.scan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.scan-item.failed {
    border-left-color: #dc3545;
}

.scan-data {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #495057;
}

.scan-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.no-scans {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* Manual Input */
.manual-input-group {
    display: flex;
    gap: 10px;
}

.manual-input {
    flex: 1;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
}

.manual-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.9;
}

.footer p {
    margin-bottom: 10px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toast {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    min-width: 250px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid #28a745;
}

.toast.error {
    border-left: 4px solid #dc3545;
}

.toast.info {
    border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    color: white;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .scanner-video {
        height: 250px;
    }
    
    .scan-frame {
        width: 150px;
        height: 150px;
    }
    
    .scan-line {
        width: 130px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .manual-input-group {
        flex-direction: column;
    }
}
