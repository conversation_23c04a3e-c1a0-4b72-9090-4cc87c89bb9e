<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barcode-to-PC Scanner</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://unpkg.com/@zxing/browser@latest/umd/index.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>📱 Barcode-to-PC</h1>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Connecting...</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Scanner Section -->
            <section class="scanner-section">
                <div class="scanner-container">
                    <video id="video" class="scanner-video" autoplay muted playsinline></video>
                    <div class="scanner-overlay">
                        <div class="scan-frame"></div>
                        <div class="scan-line"></div>
                    </div>
                </div>
                
                <div class="scanner-controls">
                    <button id="startScanBtn" class="btn btn-primary">
                        📷 Start Scanning
                    </button>
                    <button id="stopScanBtn" class="btn btn-secondary" style="display: none;">
                        ⏹️ Stop Scanning
                    </button>
                    <button id="switchCameraBtn" class="btn btn-secondary">
                        🔄 Switch Camera
                    </button>
                </div>
            </section>

            <!-- Status Section -->
            <section class="status-section">
                <div class="status-card">
                    <h3>📊 Status</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="label">Client ID:</span>
                            <span id="clientId">-</span>
                        </div>
                        <div class="status-item">
                            <span class="label">Scans Sent:</span>
                            <span id="scanCount">0</span>
                        </div>
                        <div class="status-item">
                            <span class="label">Connected Clients:</span>
                            <span id="connectedClients">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Scans Section -->
            <section class="scans-section">
                <div class="scans-card">
                    <h3>📋 Recent Scans</h3>
                    <div id="recentScans" class="scans-list">
                        <div class="no-scans">No scans yet. Start scanning to see results here.</div>
                    </div>
                </div>
            </section>

            <!-- Manual Input Section -->
            <section class="manual-section">
                <div class="manual-card">
                    <h3>⌨️ Manual Input</h3>
                    <div class="manual-input-group">
                        <input type="text" id="manualInput" placeholder="Enter barcode manually..." class="manual-input">
                        <button id="sendManualBtn" class="btn btn-primary">Send</button>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>Scan barcodes with your camera to send them to your PC</p>
            <div class="footer-links">
                <button id="reconnectBtn" class="btn btn-link">🔄 Reconnect</button>
                <button id="clearScansBtn" class="btn btn-link">🗑️ Clear Scans</button>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Initializing camera...</p>
    </div>

    <!-- Scripts -->
    <script src="js/websocket.js"></script>
    <script src="js/scanner.js"></script>
    
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Barcode-to-PC Client Starting...');
            
            // Initialize WebSocket connection
            WebSocketManager.init();
            
            // Initialize scanner
            BarcodeScanner.init();
            
            // Setup manual input
            setupManualInput();
            
            // Setup UI event handlers
            setupUIHandlers();
        });

        function setupManualInput() {
            const manualInput = document.getElementById('manualInput');
            const sendManualBtn = document.getElementById('sendManualBtn');

            function sendManualBarcode() {
                const barcode = manualInput.value.trim();
                if (barcode) {
                    WebSocketManager.sendBarcode(barcode);
                    manualInput.value = '';
                }
            }

            sendManualBtn.addEventListener('click', sendManualBarcode);
            manualInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendManualBarcode();
                }
            });
        }

        function setupUIHandlers() {
            // Reconnect button
            document.getElementById('reconnectBtn').addEventListener('click', function() {
                WebSocketManager.reconnect();
            });

            // Clear scans button
            document.getElementById('clearScansBtn').addEventListener('click', function() {
                clearRecentScans();
            });
        }

        function clearRecentScans() {
            const scansContainer = document.getElementById('recentScans');
            scansContainer.innerHTML = '<div class="no-scans">No scans yet. Start scanning to see results here.</div>';
            
            // Reset scan count
            document.getElementById('scanCount').textContent = '0';
            
            showToast('Recent scans cleared', 'info');
        }
    </script>
</body>
</html>
