# Barcode-to-PC Clone

A complete barcode scanning solution that allows you to scan barcodes with your mobile device and automatically type them on your PC. Works entirely over local Wi-Fi (LAN) with no internet connection required.

## Features

- 📱 **Mobile Web Client**: Browser-based barcode scanner using device camera
- 🖥️ **PC Server**: Python Flask server with real-time WebSocket communication
- ⌨️ **Auto-typing**: Automatically types scanned barcodes on PC using pyautogui
- 📊 **Multi-device Support**: Multiple mobile devices can connect simultaneously
- 📝 **Scan History**: Saves all scans to CSV file with timestamps
- 🔗 **QR Code Connection**: Generates QR code for easy mobile device connection
- 🔄 **Real-time Updates**: Live connection status and scan notifications
- 📱 **Mobile Optimized**: Responsive design optimized for mobile devices

## Project Structure

```
barcodeToPC/
├── server/                 # Python Flask server
│   ├── app.py             # Main server application
│   ├── barcode_handler.py # Auto-typing functionality
│   ├── qr_generator.py    # QR code generation
│   ├── history_manager.py # CSV history management
│   └── requirements.txt   # Python dependencies
├── client/                # Mobile web client
│   ├── index.html        # Main client page
│   ├── css/
│   │   └── style.css     # Mobile-optimized styles
│   └── js/
│       ├── scanner.js    # Barcode scanning logic
│       └── websocket.js  # WebSocket communication
├── static/               # Generated files (QR codes)
├── data/                 # Scan history storage
└── README.md            # This file
```

## Quick Start

### 1. Install Dependencies

```bash
# Navigate to server directory
cd barcodeToPC/server

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Start the Server

```bash
# Run the server
python app.py

# Or with custom host/port
python app.py --host 0.0.0.0 --port 5000
```

### 3. Connect Mobile Device

1. **Scan QR Code**: The server will display a QR code in the terminal and save it to `static/qr_code.png`
2. **Manual Connection**: Navigate to the displayed URL (e.g., `http://*************:5000`) on your mobile device
3. **Grant Camera Permission**: Allow camera access when prompted
4. **Start Scanning**: Tap "Start Scanning" and point camera at barcodes

## Detailed Setup Instructions

### Windows Setup

1. **Install Python** (3.7 or higher)
   ```bash
   # Download from python.org or use Microsoft Store
   ```

2. **Install Dependencies**
   ```bash
   cd barcodeToPC\server
   pip install -r requirements.txt
   ```

3. **Run Server**
   ```bash
   python app.py
   ```

### Linux/macOS Setup

1. **Install Python** (usually pre-installed)
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3 python3-pip
   
   # macOS with Homebrew
   brew install python
   ```

2. **Install Dependencies**
   ```bash
   cd barcodeToPC/server
   pip3 install -r requirements.txt
   ```

3. **Run Server**
   ```bash
   python3 app.py
   ```

## Usage

### Server Commands

```bash
# Basic usage
python app.py

# Custom host and port
python app.py --host ************* --port 8080

# Enable debug mode
python app.py --debug
```

### Mobile Client Features

- **Camera Scanning**: Real-time barcode detection using device camera
- **Manual Input**: Type barcodes manually if camera scanning fails
- **Connection Status**: Live connection indicator and reconnection
- **Scan History**: View recent scans with timestamps
- **Multi-camera Support**: Switch between front/back cameras
- **Offline Handling**: Graceful handling of connection issues

### Server Features

- **Auto-typing**: Configurable typing speed and safety delays
- **Multi-client**: Support for multiple connected devices
- **History Logging**: All scans saved to CSV with client information
- **QR Code Generation**: Automatic connection QR code creation
- **Real-time Status**: Live client count and scan statistics

## Configuration

### Auto-typing Settings

Edit `server/barcode_handler.py` to customize:

```python
# Typing delay between characters (seconds)
typing_delay = 0.1

# Safety delay before typing starts (seconds)
safety_delay = 1.0
```

### Server Settings

Edit `server/app.py` to customize:

```python
# Default host and port
host = '0.0.0.0'  # Listen on all interfaces
port = 5000       # Default port
```

### History Settings

Edit `server/history_manager.py` to customize:

```python
# CSV file location
data_dir = "../data"
filename = "scan_history.csv"
```

## Troubleshooting

### Common Issues

1. **Camera Not Working**
   - Ensure HTTPS or localhost (required for camera access)
   - Check browser permissions for camera access
   - Try different browsers (Chrome/Safari recommended)

2. **Connection Issues**
   - Verify devices are on same Wi-Fi network
   - Check firewall settings (allow port 5000)
   - Try different port if 5000 is blocked

3. **Auto-typing Not Working**
   - Ensure target application has focus
   - Check pyautogui failsafe (move mouse to corner to stop)
   - Verify typing delays are appropriate

4. **QR Code Not Generating**
   - Check write permissions in static directory
   - Verify PIL/Pillow installation
   - Check network connectivity for IP detection

### Debug Mode

Enable debug mode for detailed logging:

```bash
python app.py --debug
```

### Network Configuration

For advanced network setups:

```bash
# Bind to specific interface
python app.py --host *************

# Use different port
python app.py --port 8080

# Combine options
python app.py --host 0.0.0.0 --port 8080 --debug
```

## Security Notes

- This application is designed for local network use only
- No authentication is implemented (suitable for trusted networks)
- Camera access requires HTTPS in production environments
- Consider firewall rules for the server port

## Browser Compatibility

### Recommended Browsers
- **Mobile**: Chrome, Safari, Firefox
- **Desktop**: Chrome, Firefox, Edge, Safari

### Required Features
- WebSocket support
- Camera API (getUserMedia)
- ES6 JavaScript support

## Dependencies

### Python Server
- Flask 2.3.3
- Flask-SocketIO 5.3.6
- pyautogui 0.9.54
- qrcode 7.4.2
- Pillow 10.0.1
- python-socketio 5.9.0
- eventlet 0.33.3

### Client Libraries
- @zxing/browser (barcode scanning)
- Socket.IO client (WebSocket communication)

## License

This project is open source and available under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review server logs for errors
3. Test with different devices/browsers
4. Check network connectivity

---

**Happy Scanning!** 📱➡️🖥️
