/**
 * WebSocket Manager for Barcode-to-PC Client
 * Handles real-time communication with the server
 */

const WebSocketManager = {
    socket: null,
    isConnected: false,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectDelay: 2000,
    clientId: null,
    scanCount: 0,

    /**
     * Initialize WebSocket connection
     */
    init() {
        this.connect();
        this.setupEventHandlers();
    },

    /**
     * Connect to the WebSocket server
     */
    connect() {
        try {
            console.log('Connecting to WebSocket server...');
            this.updateConnectionStatus('Connecting...', false);

            // Initialize Socket.IO connection
            this.socket = io();

            this.setupSocketEvents();

        } catch (error) {
            console.error('Error connecting to WebSocket:', error);
            this.updateConnectionStatus('Connection failed', false);
            this.scheduleReconnect();
        }
    },

    /**
     * Setup Socket.IO event handlers
     */
    setupSocketEvents() {
        // Connection established
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus('Connected', true);
            showToast('Connected to server', 'success');
        });

        // Connection confirmed with client info
        this.socket.on('connection_confirmed', (data) => {
            console.log('Connection confirmed:', data);
            this.clientId = data.client_id;
            document.getElementById('clientId').textContent = this.clientId;
        });

        // Disconnection
        this.socket.on('disconnect', (reason) => {
            console.log('Disconnected from server:', reason);
            this.isConnected = false;
            this.updateConnectionStatus('Disconnected', false);
            showToast('Disconnected from server', 'error');
            
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, try to reconnect
                this.scheduleReconnect();
            }
        });

        // Scan result from server
        this.socket.on('scan_result', (data) => {
            console.log('Scan result:', data);
            this.handleScanResult(data);
        });

        // Scan notification from other clients
        this.socket.on('scan_notification', (data) => {
            console.log('Scan notification:', data);
            // Only show notifications from other clients
            if (data.client_id !== this.clientId) {
                showToast(`Scan from ${data.client_id}: ${data.barcode}`, 'info');
            }
        });

        // Client count update
        this.socket.on('client_count_update', (data) => {
            document.getElementById('connectedClients').textContent = data.count;
        });

        // Pong response
        this.socket.on('pong', (data) => {
            console.log('Pong received:', data);
        });

        // Error handling
        this.socket.on('error', (error) => {
            console.error('Socket error:', error);
            showToast(`Error: ${error.message || error}`, 'error');
        });

        // Connection error
        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            this.updateConnectionStatus('Connection error', false);
            this.scheduleReconnect();
        });
    },

    /**
     * Setup additional event handlers
     */
    setupEventHandlers() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                console.log('Page became visible, attempting reconnect...');
                this.reconnect();
            }
        });

        // Handle online/offline events
        window.addEventListener('online', () => {
            console.log('Network came online, attempting reconnect...');
            if (!this.isConnected) {
                this.reconnect();
            }
        });

        window.addEventListener('offline', () => {
            console.log('Network went offline');
            this.updateConnectionStatus('Offline', false);
        });
    },

    /**
     * Send barcode data to server
     * @param {string} barcode - The scanned barcode data
     */
    sendBarcode(barcode) {
        if (!this.isConnected || !this.socket) {
            showToast('Not connected to server', 'error');
            return false;
        }

        if (!barcode || typeof barcode !== 'string') {
            showToast('Invalid barcode data', 'error');
            return false;
        }

        try {
            console.log('Sending barcode:', barcode);
            
            this.socket.emit('barcode_scanned', {
                barcode: barcode.trim(),
                timestamp: new Date().toISOString(),
                client_id: this.clientId
            });

            return true;

        } catch (error) {
            console.error('Error sending barcode:', error);
            showToast('Failed to send barcode', 'error');
            return false;
        }
    },

    /**
     * Handle scan result from server
     * @param {Object} data - Scan result data
     */
    handleScanResult(data) {
        if (data.success) {
            this.scanCount++;
            document.getElementById('scanCount').textContent = this.scanCount;
            
            // Add to recent scans
            this.addToRecentScans(data);
            
            showToast(`Barcode sent: ${data.barcode}`, 'success');
        } else {
            showToast(`Scan failed: ${data.error}`, 'error');
        }
    },

    /**
     * Add scan to recent scans list
     * @param {Object} scanData - Scan data
     */
    addToRecentScans(scanData) {
        const scansContainer = document.getElementById('recentScans');
        
        // Remove "no scans" message if present
        const noScansMsg = scansContainer.querySelector('.no-scans');
        if (noScansMsg) {
            noScansMsg.remove();
        }

        // Create scan item
        const scanItem = document.createElement('div');
        scanItem.className = `scan-item ${scanData.success ? 'success' : 'failed'}`;
        
        const time = new Date(scanData.timestamp).toLocaleTimeString();
        
        scanItem.innerHTML = `
            <div class="scan-data">${scanData.barcode}</div>
            <div class="scan-time">${time}</div>
        `;

        // Add to top of list
        scansContainer.insertBefore(scanItem, scansContainer.firstChild);

        // Limit to 10 recent scans
        const scanItems = scansContainer.querySelectorAll('.scan-item');
        if (scanItems.length > 10) {
            scanItems[scanItems.length - 1].remove();
        }
    },

    /**
     * Update connection status in UI
     * @param {string} status - Status text
     * @param {boolean} connected - Connection state
     */
    updateConnectionStatus(status, connected) {
        const statusText = document.getElementById('statusText');
        const statusIndicator = document.getElementById('statusIndicator');

        statusText.textContent = status;
        
        if (connected) {
            statusIndicator.classList.add('connected');
        } else {
            statusIndicator.classList.remove('connected');
        }
    },

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.updateConnectionStatus('Connection failed', false);
            showToast('Connection failed. Please refresh the page.', 'error');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;

        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        this.updateConnectionStatus(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`, false);

        setTimeout(() => {
            if (!this.isConnected) {
                this.reconnect();
            }
        }, delay);
    },

    /**
     * Manually reconnect to server
     */
    reconnect() {
        console.log('Manual reconnect requested');
        
        if (this.socket) {
            this.socket.disconnect();
        }
        
        this.isConnected = false;
        this.reconnectAttempts = 0;
        
        setTimeout(() => {
            this.connect();
        }, 1000);
    },

    /**
     * Send ping to server
     */
    ping() {
        if (this.isConnected && this.socket) {
            this.socket.emit('ping');
        }
    },

    /**
     * Get connection status
     * @returns {Object} Connection status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            clientId: this.clientId,
            scanCount: this.scanCount,
            reconnectAttempts: this.reconnectAttempts
        };
    }
};

/**
 * Show toast notification
 * @param {string} message - Message to display
 * @param {string} type - Toast type (success, error, info)
 */
function showToast(message, type = 'info') {
    const container = document.getElementById('toastContainer');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    container.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
    
    // Remove on click
    toast.addEventListener('click', () => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });
}
