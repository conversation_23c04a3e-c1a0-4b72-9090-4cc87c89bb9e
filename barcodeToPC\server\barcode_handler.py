"""
Barcode Handler for Barcode-to-PC
Handles barcode processing and auto-typing functionality
"""

import pyautogui
import time
import threading
from datetime import datetime


class BarcodeHandler:
    def __init__(self, typing_delay=0.1, safety_delay=1.0):
        """
        Initialize barcode handler
        
        Args:
            typing_delay (float): Delay between keystrokes
            safety_delay (float): Safety delay before typing starts
        """
        self.typing_delay = typing_delay
        self.safety_delay = safety_delay
        self.is_typing = False
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True  # Move mouse to corner to stop
        pyautogui.PAUSE = typing_delay
    
    def process_barcode(self, barcode_data, client_info=None):
        """
        Process scanned barcode data
        
        Args:
            barcode_data (str): The scanned barcode text
            client_info (dict): Information about the client that sent the barcode
            
        Returns:
            dict: Processing result
        """
        try:
            # Validate barcode data
            if not barcode_data or not isinstance(barcode_data, str):
                return {
                    "success": False,
                    "error": "Invalid barcode data"
                }
            
            # Clean barcode data
            cleaned_data = barcode_data.strip()
            
            if not cleaned_data:
                return {
                    "success": False,
                    "error": "Empty barcode data"
                }
            
            # Log the barcode
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            client_id = client_info.get('id', 'unknown') if client_info else 'unknown'
            
            print(f"[{timestamp}] Barcode received from {client_id}: {cleaned_data}")
            
            # Auto-type the barcode
            self.auto_type_barcode(cleaned_data)
            
            return {
                "success": True,
                "data": cleaned_data,
                "timestamp": timestamp,
                "client_id": client_id
            }
            
        except Exception as e:
            error_msg = f"Error processing barcode: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def auto_type_barcode(self, barcode_data):
        """
        Auto-type barcode data using pyautogui
        
        Args:
            barcode_data (str): The barcode text to type
        """
        if self.is_typing:
            print("Already typing, skipping...")
            return
        
        # Use threading to avoid blocking the main thread
        typing_thread = threading.Thread(
            target=self._type_with_delay,
            args=(barcode_data,),
            daemon=True
        )
        typing_thread.start()
    
    def _type_with_delay(self, text):
        """
        Internal method to type text with safety delay
        
        Args:
            text (str): Text to type
        """
        try:
            self.is_typing = True
            
            # Safety delay to allow user to focus the target application
            print(f"Auto-typing in {self.safety_delay} seconds: {text}")
            time.sleep(self.safety_delay)
            
            # Type the barcode data
            pyautogui.write(text, interval=self.typing_delay)
            
            print(f"Successfully typed: {text}")
            
        except pyautogui.FailSafeException:
            print("Auto-typing stopped by failsafe (mouse moved to corner)")
        except Exception as e:
            print(f"Error during auto-typing: {str(e)}")
        finally:
            self.is_typing = False
    
    def configure_typing(self, typing_delay=None, safety_delay=None):
        """
        Configure typing parameters
        
        Args:
            typing_delay (float): Delay between keystrokes
            safety_delay (float): Safety delay before typing starts
        """
        if typing_delay is not None:
            self.typing_delay = typing_delay
            pyautogui.PAUSE = typing_delay
        
        if safety_delay is not None:
            self.safety_delay = safety_delay
        
        print(f"Typing configured - Delay: {self.typing_delay}s, Safety: {self.safety_delay}s")
    
    def get_status(self):
        """
        Get current handler status
        
        Returns:
            dict: Current status information
        """
        return {
            "is_typing": self.is_typing,
            "typing_delay": self.typing_delay,
            "safety_delay": self.safety_delay,
            "failsafe_enabled": pyautogui.FAILSAFE
        }


if __name__ == "__main__":
    # Test barcode handler
    handler = BarcodeHandler()
    
    # Test barcode processing
    test_barcode = "123456789"
    result = handler.process_barcode(test_barcode, {"id": "test_client"})
    print(f"Test result: {result}")
