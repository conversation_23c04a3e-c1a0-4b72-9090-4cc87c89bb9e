"""
Main Flask Server for Barcode-to-PC
Handles WebSocket connections, barcode processing, and client management
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit, disconnect
import os
import sys
import threading
import time
from datetime import datetime

# Import our custom modules
from qr_generator import QRGenerator
from barcode_handler import <PERSON>code<PERSON><PERSON><PERSON>
from history_manager import HistoryManager

# Initialize Flask app
app = Flask(__name__, 
           template_folder='../client',
           static_folder='../static')
app.config['SECRET_KEY'] = 'barcode-to-pc-secret-key'

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')

# Initialize components
qr_generator = QRGenerator()
barcode_handler = BarcodeHandler()
history_manager = HistoryManager()

# Connected clients tracking
connected_clients = {}
client_counter = 0


@app.route('/')
def index():
    """Serve the main client page"""
    return render_template('index.html')


@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('../static', filename)


@app.route('/api/status')
def api_status():
    """API endpoint for server status"""
    return jsonify({
        'status': 'running',
        'connected_clients': len(connected_clients),
        'total_scans': history_manager.get_scan_count(),
        'server_time': datetime.now().isoformat()
    })


@app.route('/api/history')
def api_history():
    """API endpoint for scan history"""
    limit = request.args.get('limit', 50, type=int)
    recent_scans = history_manager.get_recent_scans(limit)
    return jsonify({
        'scans': recent_scans,
        'total_count': history_manager.get_scan_count()
    })


@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    global client_counter
    client_counter += 1
    
    client_id = f"client_{client_counter}"
    client_ip = request.environ.get('REMOTE_ADDR', 'unknown')
    
    # Store client information
    connected_clients[request.sid] = {
        'id': client_id,
        'ip': client_ip,
        'connected_at': datetime.now().isoformat(),
        'scans_count': 0
    }
    
    print(f"Client connected: {client_id} from {client_ip}")
    
    # Send connection confirmation
    emit('connection_confirmed', {
        'client_id': client_id,
        'server_time': datetime.now().isoformat(),
        'message': 'Connected to Barcode-to-PC server'
    })
    
    # Broadcast client count update
    socketio.emit('client_count_update', {
        'count': len(connected_clients)
    })


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    if request.sid in connected_clients:
        client_info = connected_clients[request.sid]
        print(f"Client disconnected: {client_info['id']} from {client_info['ip']}")
        del connected_clients[request.sid]
        
        # Broadcast client count update
        socketio.emit('client_count_update', {
            'count': len(connected_clients)
        })


@socketio.on('barcode_scanned')
def handle_barcode_scan(data):
    """Handle barcode scan from client"""
    try:
        # Get client information
        client_info = connected_clients.get(request.sid, {})
        
        if not client_info:
            emit('error', {'message': 'Client not registered'})
            return
        
        # Extract barcode data
        barcode_data = data.get('barcode', '').strip()
        
        if not barcode_data:
            emit('scan_result', {
                'success': False,
                'error': 'Empty barcode data'
            })
            return
        
        print(f"Barcode received from {client_info['id']}: {barcode_data}")
        
        # Process the barcode
        processing_result = barcode_handler.process_barcode(barcode_data, client_info)
        
        # Save to history
        history_manager.save_scan(barcode_data, client_info, processing_result)
        
        # Update client scan count
        connected_clients[request.sid]['scans_count'] += 1
        
        # Send result back to client
        emit('scan_result', {
            'success': processing_result['success'],
            'barcode': barcode_data,
            'timestamp': processing_result.get('timestamp'),
            'error': processing_result.get('error')
        })
        
        # Broadcast scan notification to all clients
        socketio.emit('scan_notification', {
            'client_id': client_info['id'],
            'barcode': barcode_data,
            'timestamp': processing_result.get('timestamp'),
            'success': processing_result['success']
        })
        
    except Exception as e:
        error_msg = f"Error handling barcode scan: {str(e)}"
        print(error_msg)
        emit('scan_result', {
            'success': False,
            'error': error_msg
        })


@socketio.on('ping')
def handle_ping():
    """Handle ping from client"""
    emit('pong', {'timestamp': datetime.now().isoformat()})


@socketio.on('get_client_info')
def handle_get_client_info():
    """Send client information"""
    client_info = connected_clients.get(request.sid, {})
    emit('client_info', client_info)


def start_server(host='0.0.0.0', port=5000, debug=False):
    """
    Start the Barcode-to-PC server
    
    Args:
        host (str): Host address to bind to
        port (int): Port number to listen on
        debug (bool): Enable debug mode
    """
    try:
        # Generate QR code for easy connection
        qr_generator.generate_connection_qr(port)
        qr_generator.display_connection_info(port)
        
        # Start the server
        print(f"Starting Barcode-to-PC server on {host}:{port}")
        socketio.run(app, host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {str(e)}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Barcode-to-PC Server')
    parser.add_argument('--host', default='0.0.0.0', help='Host address (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port number (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    start_server(host=args.host, port=args.port, debug=args.debug)
