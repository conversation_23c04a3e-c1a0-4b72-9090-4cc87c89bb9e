"""
QR Code Generator for Barcode-to-PC
Generates QR codes containing server connection information
"""

import qrcode
import socket
import os
from PIL import Image


class QRGenerator:
    def __init__(self, static_dir="../static"):
        """Initialize QR generator with static directory path"""
        self.static_dir = static_dir
        self.ensure_static_dir()
    
    def ensure_static_dir(self):
        """Create static directory if it doesn't exist"""
        if not os.path.exists(self.static_dir):
            os.makedirs(self.static_dir)
    
    def get_local_ip(self):
        """Get the local IP address of the machine"""
        try:
            # Connect to a remote address to determine local IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
            return local_ip
        except Exception:
            # Fallback to localhost if unable to determine IP
            return "127.0.0.1"
    
    def generate_connection_qr(self, port=5000):
        """
        Generate QR code containing server connection URL
        
        Args:
            port (int): Server port number
            
        Returns:
            str: Path to generated QR code image
        """
        local_ip = self.get_local_ip()
        connection_url = f"http://{local_ip}:{port}"
        
        # Create QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(connection_url)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Save QR code
        qr_path = os.path.join(self.static_dir, "qr_code.png")
        img.save(qr_path)
        
        print(f"QR Code generated: {qr_path}")
        print(f"Connection URL: {connection_url}")
        print(f"Scan this QR code with your mobile device to connect!")
        
        return qr_path
    
    def display_connection_info(self, port=5000):
        """Display connection information in console"""
        local_ip = self.get_local_ip()
        connection_url = f"http://{local_ip}:{port}"
        
        print("\n" + "="*50)
        print("BARCODE-TO-PC SERVER STARTED")
        print("="*50)
        print(f"Server IP: {local_ip}")
        print(f"Server Port: {port}")
        print(f"Connection URL: {connection_url}")
        print("\nTo connect from mobile device:")
        print("1. Scan the QR code displayed")
        print("2. Or manually navigate to the URL above")
        print("="*50 + "\n")


if __name__ == "__main__":
    # Test QR generation
    generator = QRGenerator()
    generator.generate_connection_qr(5000)
    generator.display_connection_info(5000)
