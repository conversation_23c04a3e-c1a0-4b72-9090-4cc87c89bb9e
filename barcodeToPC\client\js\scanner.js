/**
 * Barcode Scanner for Barcode-to-PC Client
 * Uses @zxing/browser library for barcode scanning
 */

const BarcodeScanner = {
    codeReader: null,
    isScanning: false,
    currentDeviceId: null,
    availableDevices: [],
    video: null,
    scanCount: 0,

    /**
     * Initialize the barcode scanner
     */
    async init() {
        console.log('Initializing barcode scanner...');
        
        this.video = document.getElementById('video');
        this.setupUI();
        
        try {
            // Initialize ZXing code reader
            this.codeReader = new ZXing.BrowserMultiFormatReader();
            
            // Get available video devices
            await this.getVideoDevices();
            
            console.log('Barcode scanner initialized successfully');
            
        } catch (error) {
            console.error('Error initializing scanner:', error);
            showToast('Failed to initialize camera', 'error');
            this.hideLoading();
        }
    },

    /**
     * Setup UI event handlers
     */
    setupUI() {
        const startBtn = document.getElementById('startScanBtn');
        const stopBtn = document.getElementById('stopScanBtn');
        const switchBtn = document.getElementById('switchCameraBtn');

        startBtn.addEventListener('click', () => this.startScanning());
        stopBtn.addEventListener('click', () => this.stopScanning());
        switchBtn.addEventListener('click', () => this.switchCamera());
    },

    /**
     * Get available video input devices
     */
    async getVideoDevices() {
        try {
            console.log('Getting video devices...');
            
            // Request camera permissions first
            await navigator.mediaDevices.getUserMedia({ video: true });
            
            // Get all video input devices
            const devices = await this.codeReader.listVideoInputDevices();
            this.availableDevices = devices;
            
            console.log(`Found ${devices.length} video devices:`, devices);
            
            if (devices.length === 0) {
                throw new Error('No camera devices found');
            }
            
            // Use the first device by default (usually back camera on mobile)
            this.currentDeviceId = devices[0].deviceId;
            
            // Update switch camera button visibility
            const switchBtn = document.getElementById('switchCameraBtn');
            switchBtn.style.display = devices.length > 1 ? 'inline-flex' : 'none';
            
            this.hideLoading();
            
        } catch (error) {
            console.error('Error getting video devices:', error);
            showToast('Camera access denied or not available', 'error');
            this.hideLoading();
            throw error;
        }
    },

    /**
     * Start barcode scanning
     */
    async startScanning() {
        if (this.isScanning) {
            console.log('Already scanning');
            return;
        }

        if (!this.codeReader || !this.currentDeviceId) {
            showToast('Scanner not initialized', 'error');
            return;
        }

        try {
            console.log('Starting barcode scanning...');
            this.showLoading('Starting camera...');
            
            this.isScanning = true;
            this.updateScanButtons();

            // Start decoding from video device
            await this.codeReader.decodeFromVideoDevice(
                this.currentDeviceId,
                this.video,
                (result, error) => {
                    if (result) {
                        this.handleScanResult(result);
                    }
                    
                    if (error && !(error instanceof ZXing.NotFoundException)) {
                        console.error('Scan error:', error);
                    }
                }
            );

            console.log('Barcode scanning started');
            this.hideLoading();
            showToast('Camera started - Point at barcode to scan', 'info');

        } catch (error) {
            console.error('Error starting scanner:', error);
            this.isScanning = false;
            this.updateScanButtons();
            this.hideLoading();
            showToast('Failed to start camera', 'error');
        }
    },

    /**
     * Stop barcode scanning
     */
    stopScanning() {
        if (!this.isScanning) {
            console.log('Not currently scanning');
            return;
        }

        try {
            console.log('Stopping barcode scanning...');
            
            this.codeReader.reset();
            this.isScanning = false;
            this.updateScanButtons();
            
            console.log('Barcode scanning stopped');
            showToast('Camera stopped', 'info');

        } catch (error) {
            console.error('Error stopping scanner:', error);
            showToast('Error stopping camera', 'error');
        }
    },

    /**
     * Switch to next available camera
     */
    async switchCamera() {
        if (this.availableDevices.length <= 1) {
            showToast('No other cameras available', 'info');
            return;
        }

        try {
            console.log('Switching camera...');
            
            // Find current device index
            const currentIndex = this.availableDevices.findIndex(
                device => device.deviceId === this.currentDeviceId
            );
            
            // Switch to next device (or first if at end)
            const nextIndex = (currentIndex + 1) % this.availableDevices.length;
            this.currentDeviceId = this.availableDevices[nextIndex].deviceId;
            
            console.log(`Switched to camera: ${this.availableDevices[nextIndex].label}`);
            
            // Restart scanning with new device if currently scanning
            if (this.isScanning) {
                this.stopScanning();
                setTimeout(() => {
                    this.startScanning();
                }, 500);
            }
            
            showToast(`Switched to: ${this.availableDevices[nextIndex].label || 'Camera ' + (nextIndex + 1)}`, 'info');

        } catch (error) {
            console.error('Error switching camera:', error);
            showToast('Failed to switch camera', 'error');
        }
    },

    /**
     * Handle successful barcode scan
     * @param {Object} result - ZXing scan result
     */
    handleScanResult(result) {
        const barcode = result.getText();
        console.log('Barcode scanned:', barcode);

        // Send barcode to server
        const success = WebSocketManager.sendBarcode(barcode);
        
        if (success) {
            this.scanCount++;
            
            // Visual feedback
            this.showScanFeedback();
            
            // Vibrate if supported
            if (navigator.vibrate) {
                navigator.vibrate(100);
            }
            
            console.log(`Total scans: ${this.scanCount}`);
        }
    },

    /**
     * Show visual feedback for successful scan
     */
    showScanFeedback() {
        const scanFrame = document.querySelector('.scan-frame');
        if (scanFrame) {
            scanFrame.style.borderColor = '#00ff00';
            scanFrame.style.boxShadow = '0 0 30px rgba(0,255,0,0.8)';
            
            setTimeout(() => {
                scanFrame.style.borderColor = '#00ff00';
                scanFrame.style.boxShadow = '0 0 20px rgba(0,255,0,0.5)';
            }, 200);
        }
    },

    /**
     * Update scan button states
     */
    updateScanButtons() {
        const startBtn = document.getElementById('startScanBtn');
        const stopBtn = document.getElementById('stopScanBtn');
        const switchBtn = document.getElementById('switchCameraBtn');

        if (this.isScanning) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-flex';
            switchBtn.disabled = false;
        } else {
            startBtn.style.display = 'inline-flex';
            stopBtn.style.display = 'none';
            switchBtn.disabled = false;
        }
    },

    /**
     * Show loading overlay
     * @param {string} message - Loading message
     */
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('p');
        
        if (text) {
            text.textContent = message;
        }
        
        overlay.classList.remove('hidden');
    },

    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.add('hidden');
    },

    /**
     * Get scanner status
     * @returns {Object} Scanner status
     */
    getStatus() {
        return {
            isScanning: this.isScanning,
            currentDeviceId: this.currentDeviceId,
            availableDevices: this.availableDevices.length,
            scanCount: this.scanCount,
            hasCamera: !!this.codeReader
        };
    },

    /**
     * Cleanup scanner resources
     */
    cleanup() {
        try {
            if (this.isScanning) {
                this.stopScanning();
            }
            
            if (this.codeReader) {
                this.codeReader.reset();
            }
            
            console.log('Scanner cleanup completed');
            
        } catch (error) {
            console.error('Error during scanner cleanup:', error);
        }
    }
};

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    BarcodeScanner.cleanup();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden && BarcodeScanner.isScanning) {
        console.log('Page hidden, stopping scanner');
        BarcodeScanner.stopScanning();
    }
});

// Auto-start scanning when page becomes visible (optional)
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && !BarcodeScanner.isScanning && BarcodeScanner.codeReader) {
        // Uncomment the next line to auto-start scanning when page becomes visible
        // setTimeout(() => BarcodeScanner.startScanning(), 1000);
    }
});
