"""
History Manager for Barcode-to-PC
Manages scan history storage and retrieval using CSV files
"""

import csv
import os
from datetime import datetime
from typing import List, Dict, Optional


class HistoryManager:
    def __init__(self, data_dir="../data", filename="scan_history.csv"):
        """
        Initialize history manager
        
        Args:
            data_dir (str): Directory to store data files
            filename (str): CSV filename for scan history
        """
        self.data_dir = data_dir
        self.filename = filename
        self.filepath = os.path.join(data_dir, filename)
        self.ensure_data_dir()
        self.ensure_csv_file()
    
    def ensure_data_dir(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            print(f"Created data directory: {self.data_dir}")
    
    def ensure_csv_file(self):
        """Create CSV file with headers if it doesn't exist"""
        if not os.path.exists(self.filepath):
            self.create_csv_file()
    
    def create_csv_file(self):
        """Create new CSV file with appropriate headers"""
        headers = [
            "timestamp",
            "barcode_data",
            "client_id",
            "client_ip",
            "processing_status"
        ]
        
        try:
            with open(self.filepath, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(headers)
            print(f"Created scan history file: {self.filepath}")
        except Exception as e:
            print(f"Error creating CSV file: {str(e)}")
    
    def save_scan(self, barcode_data: str, client_info: Dict, processing_result: Dict):
        """
        Save a barcode scan to history
        
        Args:
            barcode_data (str): The scanned barcode data
            client_info (dict): Information about the client
            processing_result (dict): Result of barcode processing
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            client_id = client_info.get('id', 'unknown')
            client_ip = client_info.get('ip', 'unknown')
            status = "success" if processing_result.get('success', False) else "failed"
            
            row = [
                timestamp,
                barcode_data,
                client_id,
                client_ip,
                status
            ]
            
            with open(self.filepath, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(row)
            
            print(f"Saved scan to history: {barcode_data} from {client_id}")
            
        except Exception as e:
            print(f"Error saving scan to history: {str(e)}")
    
    def get_recent_scans(self, limit: int = 50) -> List[Dict]:
        """
        Get recent scans from history
        
        Args:
            limit (int): Maximum number of scans to return
            
        Returns:
            List[Dict]: List of recent scan records
        """
        try:
            scans = []
            
            if not os.path.exists(self.filepath):
                return scans
            
            with open(self.filepath, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                all_scans = list(reader)
                
                # Return most recent scans (reverse order)
                recent_scans = all_scans[-limit:] if len(all_scans) > limit else all_scans
                recent_scans.reverse()
                
                return recent_scans
                
        except Exception as e:
            print(f"Error reading scan history: {str(e)}")
            return []
    
    def get_scan_count(self) -> int:
        """
        Get total number of scans in history
        
        Returns:
            int: Total scan count
        """
        try:
            if not os.path.exists(self.filepath):
                return 0
            
            with open(self.filepath, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                # Subtract 1 for header row
                return sum(1 for row in reader) - 1
                
        except Exception as e:
            print(f"Error counting scans: {str(e)}")
            return 0
    
    def search_scans(self, query: str, limit: int = 20) -> List[Dict]:
        """
        Search scans by barcode data
        
        Args:
            query (str): Search query
            limit (int): Maximum results to return
            
        Returns:
            List[Dict]: Matching scan records
        """
        try:
            if not query or not os.path.exists(self.filepath):
                return []
            
            matches = []
            query_lower = query.lower()
            
            with open(self.filepath, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    barcode_data = row.get('barcode_data', '').lower()
                    if query_lower in barcode_data:
                        matches.append(row)
                        
                        if len(matches) >= limit:
                            break
            
            # Return most recent matches first
            matches.reverse()
            return matches
            
        except Exception as e:
            print(f"Error searching scans: {str(e)}")
            return []
    
    def clear_history(self) -> bool:
        """
        Clear all scan history
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.create_csv_file()
            print("Scan history cleared")
            return True
        except Exception as e:
            print(f"Error clearing history: {str(e)}")
            return False
    
    def export_history(self, export_path: str) -> bool:
        """
        Export history to a different location
        
        Args:
            export_path (str): Path to export the history file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not os.path.exists(self.filepath):
                print("No history file to export")
                return False
            
            import shutil
            shutil.copy2(self.filepath, export_path)
            print(f"History exported to: {export_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting history: {str(e)}")
            return False


if __name__ == "__main__":
    # Test history manager
    manager = HistoryManager()
    
    # Test saving a scan
    test_client = {"id": "test_client", "ip": "*************"}
    test_result = {"success": True}
    manager.save_scan("123456789", test_client, test_result)
    
    # Test getting recent scans
    recent = manager.get_recent_scans(10)
    print(f"Recent scans: {recent}")
    
    # Test scan count
    count = manager.get_scan_count()
    print(f"Total scans: {count}")
